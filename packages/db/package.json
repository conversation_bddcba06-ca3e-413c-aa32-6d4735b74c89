{"name": "@cindee/db", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./client": {"types": "./dist/client.d.ts", "default": "./src/client.ts"}, "./schema": {"types": "./dist/schema.d.ts", "default": "./src/schema.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm with-env drizzle-kit push", "studio": "pnpm with-env drizzle-kit studio", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@vercel/postgres": "^0.10.0", "drizzle-orm": "^0.44.5", "drizzle-zod": "^0.8.3", "zod": "catalog:"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "dotenv-cli": "^10.0.0", "drizzle-kit": "^0.31.4", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config"}