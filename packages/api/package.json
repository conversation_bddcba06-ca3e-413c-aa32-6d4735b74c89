{"name": "@cindee/api", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@cindee/auth": "workspace:*", "@cindee/db": "workspace:*", "@cindee/validators": "workspace:*", "@trpc/server": "catalog:", "superjson": "2.2.2", "zod": "catalog:"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config"}