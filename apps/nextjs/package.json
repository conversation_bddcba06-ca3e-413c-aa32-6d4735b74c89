{"name": "@cindee/nextjs", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@cindee/api": "workspace:*", "@cindee/auth": "workspace:*", "@cindee/db": "workspace:*", "@cindee/ui": "workspace:*", "@cindee/validators": "workspace:*", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "catalog:", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@trpc/tanstack-react-query": "catalog:", "next": "^15.5.2", "react": "catalog:react19", "react-dom": "catalog:react19", "superjson": "2.2.2", "zod": "catalog:"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tailwind-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "@types/node": "^22.18.1", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "dotenv-cli": "^10.0.0", "eslint": "catalog:", "jiti": "^2.5.1", "prettier": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config"}